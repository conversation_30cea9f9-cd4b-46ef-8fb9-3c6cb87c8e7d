# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
dist
dist-ssr
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Server uploads
/server/uploads/*
!/server/uploads/.gitkeep

# Build directories
/build
/dist

# Cache
.cache
.parcel-cache

# Backend/Server files (exclude from frontend)
backend/
server/
sql/
supabase/

# Documentation that should be in backend
docs/
*.md
!README.md

# Database and migration files
*.sql
*.db
*.sqlite

# Python files
*.py
*.pyc
__pycache__/
requirements.txt
alembic.ini

# Shell scripts
*.sh
*.bat
*.ps1

# YAML configs for backend
render.yaml
