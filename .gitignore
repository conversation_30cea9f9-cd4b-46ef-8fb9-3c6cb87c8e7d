# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
<<<<<<< HEAD
node_modules
dist
dist-ssr
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
=======
node_modules/
dist/
dist-ssr/
*.local

# Environment variables (SENSITIVE - DO NOT COMMIT)
.env
.env.*
!.env.example
>>>>>>> master

# Editor directories and files
.vscode/*
!.vscode/extensions.json
<<<<<<< HEAD
.idea
=======
.idea/
>>>>>>> master
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

<<<<<<< HEAD
# Server uploads
=======
# Server uploads (user generated content)
>>>>>>> master
/server/uploads/*
!/server/uploads/.gitkeep

# Build directories
<<<<<<< HEAD
/build
/dist

# Cache
.cache
.parcel-cache

# Backend/Server files (exclude from frontend)
backend/
server/
sql/
supabase/

# Documentation that should be in backend
docs/
*.md
!README.md

# Database and migration files
*.sql
*.db
*.sqlite

# Python files
*.py
*.pyc
__pycache__/
requirements.txt
alembic.ini

# Shell scripts
*.sh
*.bat
*.ps1

# YAML configs for backend
render.yaml
=======
/build/
/dist/

# Cache directories
.cache/
.parcel-cache/
__pycache__/
*.pyc
*.pyo

# Python virtual environments
venv/
env/
.venv/

# Database files (local development)
*.db
*.sqlite
*.sqlite3

# OS generated files
Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# IDE files
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
>>>>>>> master
