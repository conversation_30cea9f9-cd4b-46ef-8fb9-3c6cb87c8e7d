{
  "name": "invoyisi-frontend",
  "private": true,
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "lint": "eslint .",
    "preview": "vite preview",
    "start": "vite preview --port 3000"
  },
  "dependencies": {
    "@stripe/stripe-js": "^3.0.7",
    "@supabase/supabase-js": "^2.39.7",
    "axios": "^1.9.0",
<<<<<<< HEAD
=======
    "file-saver": "^2.0.5",
    "html2canvas": "^1.4.1",
    "jspdf": "^3.0.1",
>>>>>>> master
    "lucide-react": "^0.344.0",
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "react-router-dom": "^6.22.3",
    "recharts": "^2.15.3",
    "stripe": "^14.19.0",
<<<<<<< HEAD
    "tesseract.js": "^5.0.5"
=======
    "tesseract.js": "^5.0.5",
    "xlsx": "^0.18.5"
>>>>>>> master
  },
  "devDependencies": {
    "@eslint/js": "^9.9.1",
    "@types/react": "^18.3.5",
    "@types/react-dom": "^18.3.0",
    "@vitejs/plugin-react": "^4.3.1",
    "autoprefixer": "^10.4.18",
    "eslint": "^9.9.1",
    "eslint-plugin-react-hooks": "^5.1.0-rc.0",
    "eslint-plugin-react-refresh": "^0.4.11",
    "globals": "^15.9.0",
    "postcss": "^8.4.35",
    "supabase": "^1.145.4",
    "tailwindcss": "^3.4.1",
    "typescript": "^5.5.3",
    "typescript-eslint": "^8.3.0",
    "vite": "^5.4.2"
  }
}
